package service

import (
	"context"
	v1 "shikeyinxiang/api/v1"
)

// IUserService 用户服务接口，定义了所有与用户管理相关的业务能力
type IUserService interface {
	// GetUserByID 根据ID获取用户信息
	GetUserByID(ctx context.Context, id int64) (*v1.UserInfoResponse, error)

	// GetUserByUsername 根据用户名获取用户信息
	GetUserByUsername(ctx context.Context, username string) (*v1.UserInfoResponse, error)

	// GetUserByEmail 根据邮箱获取用户信息
	GetUserByEmail(ctx context.Context, email string) (*v1.UserInfoResponse, error)

	// GetUserByOpenid 根据微信openid获取用户信息
	GetUserByOpenid(ctx context.Context, openid string) (*v1.UserInfoResponse, error)

	// CreateUser 创建新用户
	CreateUser(ctx context.Context, req *v1.UserCreateReq) (*v1.UserInfoResponse, error)

	// UpdateUser 更新用户信息
	UpdateUser(ctx context.Context, req *v1.UserUpdateReq) (*v1.UserInfoResponse, error)

	// UpdateUserStatus 更新用户状态
	UpdateUserStatus(ctx context.Context, userID int64, req *v1.UserStatusUpdateReq) error

	// DeleteUser 删除用户（软删除）
	DeleteUser(ctx context.Context, id int64) error

	// GetUserInfoPage 分页查询用户信息
	GetUserInfoPage(ctx context.Context, req *v1.UserQueryReq) (*v1.UserListResponse, error)

	// UpdateUserAvatar 更新用户头像URL
	UpdateUserAvatar(ctx context.Context, userID int64, avatarURL string) error

	// VerifyPassword 验证用户密码
	VerifyPassword(ctx context.Context, usernameOrEmail, password string) (bool, error)

	// ChangePassword 修改用户密码
	ChangePassword(ctx context.Context, userID int64, req *v1.PasswordUpdateReq) error

	// GetTotalUserCount 获取系统总用户数
	GetTotalUserCount(ctx context.Context) (int64, error)

	// GenerateAvatarUploadURL 生成用户头像上传URL
	GenerateAvatarUploadURL(ctx context.Context, userID int64, contentType string) (*v1.AvatarResponse, error)

	// GenerateAvatarDownloadURL 生成用户头像下载URL
	GenerateAvatarDownloadURL(ctx context.Context, userID int64) (*v1.AvatarResponse, error)
}

// IUserNutritionGoalService 用户营养目标服务接口，定义了所有与用户营养目标管理相关的业务能力
type IUserNutritionGoalService interface {
	// GetNutritionGoal 根据用户ID获取营养目标
	GetNutritionGoal(ctx context.Context, userID int64) (*v1.UserNutritionGoalResponse, error)

	// CreateNutritionGoal 创建用户营养目标
	CreateNutritionGoal(ctx context.Context, req *v1.UserNutritionGoalCreateReq) (*v1.UserNutritionGoalResponse, error)

	// UpdateNutritionGoal 更新用户营养目标
	UpdateNutritionGoal(ctx context.Context, req *v1.UserNutritionGoalUpdateReq) (*v1.UserNutritionGoalResponse, error)

	// DeleteNutritionGoal 删除用户营养目标
	DeleteNutritionGoal(ctx context.Context, id int64) error

	// CreateDefaultNutritionGoal 创建默认营养目标
	CreateDefaultNutritionGoal(ctx context.Context, userID int64) (*v1.UserNutritionGoalResponse, error)
}
