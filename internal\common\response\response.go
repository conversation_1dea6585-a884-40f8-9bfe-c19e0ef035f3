package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"shikeyinxiang/internal/consts"
)

// ApiResponse 统一API响应格式
type ApiResponse struct {
	Code    interface{} `json:"code"`    // 可以是 int 或 string
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// PageResult 分页结果
type PageResult struct {
	Total   int64       `json:"total"`   // 总记录数
	Records interface{} `json:"records"` // 记录列表
	Current int         `json:"current"` // 当前页码
	Size    int         `json:"size"`    // 每页大小
}

// getHTTPStatus 根据业务错误码获取HTTP状态码
func getHTTPStatus(code int) int {
	switch {
	case code >= 400 && code < 500:
		return code // 直接使用HTTP状态码
	case code >= 1000 && code < 2000: // 用户相关错误
		switch code {
		case consts.CodeAuthInvalidToken, consts.CodeAuthMissingToken, consts.CodeAuthFormatError, consts.CodeAuthExpired:
			return http.StatusUnauthorized
		case consts.CodeUserPasswordError:
			return http.StatusUnauthorized
		case consts.CodeUsernameExists, consts.CodeEmailExists:
			return http.StatusConflict
		default:
			return http.StatusBadRequest
		}
	default:
		return http.StatusOK // 成功或其他情况
	}
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    consts.Success,
		Message: consts.GetMessage(consts.Success),
		Data:    data,
	})
}

// Error 错误响应 - 自动映射HTTP状态码
func Error(c *gin.Context, code int) {
	httpStatus := getHTTPStatus(code)
	c.JSON(httpStatus, ApiResponse{
		Code:    code,
		Message: consts.GetMessage(code),
		Data:    nil,
	})
}

// ErrorWithMessage 错误响应（自定义消息）
func ErrorWithMessage(c *gin.Context, code int, message string) {
	httpStatus := getHTTPStatus(code)
	c.JSON(httpStatus, ApiResponse{
		Code:    code,
		Message: message,
		Data:    nil,
	})
}

// SuccessWithPage 分页成功响应
func SuccessWithPage(c *gin.Context, data interface{}, total int64, current, size int) {
	pageResult := PageResult{
		Total:   total,
		Records: data,
		Current: current,
		Size:    size,
	}
	Success(c, pageResult)
}

// ErrorWithCodeAndMessage 使用业务错误码的错误响应
func ErrorWithCodeAndMessage(c *gin.Context, httpStatus int, code, message string) {
	c.JSON(httpStatus, ApiResponse{
		Code:    code,
		Message: message,
		Data:    nil,
	})
}

// SuccessWithCode 使用业务码的成功响应（保持原有的数字200）
func SuccessWithCode(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    consts.Success, // 使用数字 200
		Message: consts.GetMessage(consts.Success),
		Data:    data,
	})
}
