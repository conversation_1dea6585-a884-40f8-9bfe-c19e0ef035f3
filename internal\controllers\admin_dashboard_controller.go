package controllers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/service"
)

// AdminDashboardController 管理员仪表盘控制器，处理管理员仪表盘相关的HTTP请求
type AdminDashboardController struct {
	dashboardSvc service.IDashboardService
}

// NewAdminDashboardController 创建一个新的 AdminDashboardController 实例
func NewAdminDashboardController(dashboardSvc service.IDashboardService) *AdminDashboardController {
	return &AdminDashboardController{
		dashboardSvc: dashboardSvc,
	}
}

// bindQueryAndValidate 统一处理Query参数绑定和验证
func (adc *AdminDashboardController) bindQueryAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindQuery(req); err != nil {
		response.Error(c, consts.CodeParameterError)
		return false
	}
	return true
}

// parseIDParam 解析路径参数中的ID
func (adc *AdminDashboardController) parseIDParam(c *gin.Context, paramName string) (int64, bool) {
	idStr := c.Param(paramName)
	if idStr == "" {
		response.Error(c, consts.CodeParameterError)
		return 0, false
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || id <= 0 {
		response.Error(c, consts.CodeParameterError)
		return 0, false
	}

	return id, true
}

// GetDashboardStats 获取管理员仪表盘统计数据
// GET /api/admin/dashboard/stats
func (adc *AdminDashboardController) GetDashboardStats(c *gin.Context) {
	// 创建请求对象（目前不需要参数，但保持结构一致）
	req := &v1.DashboardStatsRequest{}

	// 调用服务层
	res, err := adc.dashboardSvc.GetDashboardStats(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GetNutritionTrend 获取用户营养摄入趋势数据
// GET /api/admin/dashboard/nutrition-trend?period=month
func (adc *AdminDashboardController) GetNutritionTrend(c *gin.Context) {
	// 获取查询参数
	period := c.DefaultQuery("period", "month")

	// 创建请求对象
	req := &v1.NutritionTrendRequest{
		Period: period,
	}

	// 调用服务层
	res, err := adc.dashboardSvc.GetNutritionTrend(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GetLatestDietRecords 获取最新饮食记录列表
// GET /api/admin/dashboard/latest-diet-records?page=1&size=10
func (adc *AdminDashboardController) GetLatestDietRecords(c *gin.Context) {
	// 绑定查询参数
	var req v1.DietRecordQueryRequest
	if !adc.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	// 调用服务层
	res, err := adc.dashboardSvc.GetLatestDietRecords(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 使用分页响应格式
	response.SuccessWithPage(c, res.Records, res.Total, res.Current, res.Size)
}

// GetDietRecordDetail 获取饮食记录详情
// GET /api/admin/dashboard/diet-record/{recordId}
func (adc *AdminDashboardController) GetDietRecordDetail(c *gin.Context) {
	// 解析路径参数
	recordID, ok := adc.parseIDParam(c, "recordId")
	if !ok {
		return
	}

	// 调用服务层
	res, err := adc.dashboardSvc.GetDietRecordDetail(c.Request.Context(), recordID)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GetPopularFoods 获取热门食物统计数据
// GET /api/admin/dashboard/popular-foods?period=month
func (adc *AdminDashboardController) GetPopularFoods(c *gin.Context) {
	// 获取查询参数
	period := c.DefaultQuery("period", "month")
	limitStr := c.DefaultQuery("limit", "10")

	// 解析limit参数
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10 // 默认限制10条
	}

	// 创建请求对象
	req := &v1.PopularFoodsRequest{
		Period: period,
		Limit:  limit,
	}

	// 调用服务层
	res, err := adc.dashboardSvc.GetPopularFoods(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}
