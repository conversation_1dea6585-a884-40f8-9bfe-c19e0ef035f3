package user

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/repositories"
	"strconv"
)

// IUserRepo 定义了用户仓库需要实现的所有方法
type IUserRepo interface {
	Create(user *entities.User) error
	GetByID(id int64) (*entities.User, error)
	GetByUsername(username string) (*entities.User, error)
	GetByEmail(email string) (*entities.User, error)
	GetByUsernameOrEmail(usernameOrEmail string) (*entities.User, error)
	GetByOpenID(openID string) (*entities.User, error)
	Update(user *entities.User) error
	Delete(id int64) error
	ExistsByUsername(username string) (bool, error)
	ExistsByEmail(email string) (bool, error)
	ExistsByOpenID(openID string) (bool, error)
	List(offset, limit int) ([]*entities.User, int64, error)
	UpdateStatus(id int64, status int8) error
	UpdatePassword(id int64, hashedPassword string) error
	GetTotalCount() (int64, error)
}

// UserRepository 用户仓储
type userRepository struct {
	db *gorm.DB
}

// NewUserRepository 创建用户仓储实例
func NewUserRepository(db *gorm.DB) IUserRepo {
	return &userRepository{
		db: db,
	}
}

// 确保 userRepository 实现了 IUserRepo 接口
var _ IUserRepo = &userRepository{}

// Create 创建新用户
func (r *userRepository) Create(user *entities.User) error {
	if err := r.db.Create(user).Error; err != nil {
		// 检查是否是重复键错误
		if repositories.IsDuplicateKeyError(err) {
			if repositories.ContainsField(err.Error(), "username") {
				return &repositories.UserExistsError{Field: "username", Value: user.Username}
			}
			if repositories.ContainsField(err.Error(), "email") {
				return &repositories.UserExistsError{Field: "email", Value: user.Email}
			}
			if repositories.ContainsField(err.Error(), "openid") {
				return &repositories.UserExistsError{Field: "openid", Value: user.OpenID}
			}
		}
		return &repositories.DatabaseError{
			Operation: "create",
			Table:     "users",
			Err:       err,
		}
	}
	return nil
}

// GetByID 根据ID获取用户
func (r *userRepository) GetByID(id int64) (*entities.User, error) {
	var user entities.User
	if err := r.db.Where("id = ?", id).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, &repositories.UserNotFoundError{
				Field: "id",
				Value: strconv.FormatInt(id, 10),
			}
		}
		return nil, fmt.Errorf("dao: query user by id=%d: %w", id, err)
	}
	return &user, nil
}

// GetByUsername 根据用户名获取用户
func (r *userRepository) GetByUsername(username string) (*entities.User, error) {
	var user entities.User
	if err := r.db.Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, &repositories.UserNotFoundError{
				Field: "username",
				Value: username,
			}
		}
		return nil, fmt.Errorf("dao: query user by username=%s: %w", username, err)
	}
	return &user, nil
}

// GetByEmail 根据邮箱获取用户
func (r *userRepository) GetByEmail(email string) (*entities.User, error) {
	var user entities.User
	if err := r.db.Where("email = ?", email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, &repositories.UserNotFoundError{
				Field: "email",
				Value: email,
			}
		}
		return nil, fmt.Errorf("dao: query user by email=%s: %w", email, err)
	}
	return &user, nil
}

// GetByUsernameOrEmail 根据用户名或邮箱获取用户（用于登录）
func (r *userRepository) GetByUsernameOrEmail(usernameOrEmail string) (*entities.User, error) {
	var user entities.User
	if err := r.db.Where("username = ? OR email = ?", usernameOrEmail, usernameOrEmail).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, &repositories.UserNotFoundError{
				Field: "username_or_email",
				Value: usernameOrEmail,
			}
		}
		return nil, fmt.Errorf("dao: query user by username_or_email=%s: %w", usernameOrEmail, err)
	}
	return &user, nil
}

// Update 更新用户信息
func (r *userRepository) Update(user *entities.User) error {
	if err := r.db.Save(user).Error; err != nil {
		return fmt.Errorf("dao: update user id=%d: %w", user.ID, err)
	}
	return nil
}

// Delete 删除用户（软删除）
func (r *userRepository) Delete(id int64) error {
	if err := r.db.Model(&entities.User{}).Where("id = ?", id).Update("status", 0).Error; err != nil {
		return fmt.Errorf("dao: delete user id=%d: %w", id, err)
	}
	return nil
}

// ExistsByUsername 检查用户名是否已存在
func (r *userRepository) ExistsByUsername(username string) (bool, error) {
	var count int64
	if err := r.db.Model(&entities.User{}).Where("username = ?", username).Count(&count).Error; err != nil {
		return false, fmt.Errorf("dao: check username exists username=%s: %w", username, err)
	}
	return count > 0, nil
}

// ExistsByEmail 检查邮箱是否已存在
func (r *userRepository) ExistsByEmail(email string) (bool, error) {
	var count int64
	if err := r.db.Model(&entities.User{}).Where("email = ?", email).Count(&count).Error; err != nil {
		return false, fmt.Errorf("dao: check email exists email=%s: %w", email, err)
	}
	return count > 0, nil
}

// List 获取用户列表（分页）
func (r *userRepository) List(offset, limit int) ([]*entities.User, int64, error) {
	var users []*entities.User
	var total int64

	// 获取总数
	if err := r.db.Model(&entities.User{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: count users: %w", err)
	}

	// 获取分页数据
	if err := r.db.Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: list users offset=%d limit=%d: %w", offset, limit, err)
	}

	return users, total, nil
}

// UpdateStatus 更新用户状态
func (r *userRepository) UpdateStatus(id int64, status int8) error {
	if err := r.db.Model(&entities.User{}).Where("id = ?", id).Update("status", status).Error; err != nil {
		return fmt.Errorf("dao: update user status id=%d status=%d: %w", id, status, err)
	}
	return nil
}

// UpdatePassword 更新用户密码
func (r *userRepository) UpdatePassword(id int64, hashedPassword string) error {
	if err := r.db.Model(&entities.User{}).Where("id = ?", id).Update("password", hashedPassword).Error; err != nil {
		return fmt.Errorf("dao: update user password id=%d: %w", id, err)
	}
	return nil
}

// GetByOpenID 根据微信OpenID获取用户
func (r *userRepository) GetByOpenID(openID string) (*entities.User, error) {
	var user entities.User
	if err := r.db.Where("openid = ?", openID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, &repositories.UserNotFoundError{
				Field: "openid",
				Value: openID,
			}
		}
		return nil, fmt.Errorf("dao: query user by openid=%s: %w", openID, err)
	}
	return &user, nil
}

// ExistsByOpenID 检查OpenID是否已存在
func (r *userRepository) ExistsByOpenID(openID string) (bool, error) {
	var count int64
	if err := r.db.Model(&entities.User{}).Where("openid = ?", openID).Count(&count).Error; err != nil {
		return false, fmt.Errorf("dao: check openid exists openid=%s: %w", openID, err)
	}
	return count > 0, nil
}



// GetTotalCount 获取系统总用户数
func (r *userRepository) GetTotalCount() (int64, error) {
	var count int64
	if err := r.db.Model(&entities.User{}).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("dao: get total user count: %w", err)
	}
	return count, nil
}


