package middleware

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"log"
	"net/http"
	"strings"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/logic"
	"shikeyinxiang/internal/pkg/errs"
	"shikeyinxiang/internal/repositories"
)

// ErrorHandler 是一个全局错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 在所有请求处理完毕后，检查是否有错误
		if len(c.Errors) == 0 {
			// 检查路由是否存在
			if c.Writer.Status() == http.StatusNotFound {
				response.Error(c, consts.CodeNotFound)
			}
			return
		}

		// 只处理最新的一个错误
		err := c.Errors.Last().Err

		// 优先处理统一的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			handleCodeError(c, codeErr)
			return
		}

		// 记录错误日志（包含请求上下文）
		log.Printf("Request error [%s %s]: %v", c.Request.Method, c.Request.URL.Path, err)

		// 处理Service层的自定义错误类型
		if handleServiceErrors(c, err) {
			return
		}

		// 处理Repository层的自定义错误类型
		if handleRepositoryErrors(c, err) {
			return
		}

		// 对于参数绑定等错误
		if c.Writer.Status() == http.StatusBadRequest || c.Writer.Status() == 0 {
			log.Printf("Parameter or client error: %v", err)
			response.Error(c, consts.CodeParameterError)
			return
		}

		// 未知的服务器内部错误
		log.Printf("Internal server error: %v", err)
		response.Error(c, consts.CodeInternalError)
	}
}

// handleServiceErrors 处理Service层的自定义错误类型
func handleServiceErrors(c *gin.Context, err error) bool {
	// 参数错误
	var paramError *logic.ParameterError
	if errors.As(err, &paramError) {
		response.Error(c, consts.CodeParameterError)
		return true
	}

	// 功能未实现错误
	var notImplemented *logic.NotImplementedError
	if errors.As(err, &notImplemented) {
		response.Error(c, consts.CodeInternalError)
		return true
	}

	// 无效凭据错误（用户名/邮箱或密码错误）
	var invalidCreds *logic.InvalidCredentialsError
	if errors.As(err, &invalidCreds) {
		response.Error(c, consts.CodeUserPasswordError)
		return true
	}

	// 账户被禁用错误
	var accountDisabled *logic.AccountDisabledError
	if errors.As(err, &accountDisabled) {
		response.Error(c, consts.CodeUserDisabled)
		return true
	}

	// 权限不足错误
	var insufficientPerms *logic.InsufficientPermissionsError
	if errors.As(err, &insufficientPerms) {
		response.Error(c, consts.CodeForbidden)
		return true
	}

	// 用户已存在错误
	var userExists *logic.UserAlreadyExistsError
	if errors.As(err, &userExists) {
		if userExists.Field == "username" {
			response.Error(c, consts.CodeUsernameExists)
		} else if userExists.Field == "email" {
			response.Error(c, consts.CodeEmailExists)
		} else {
			response.Error(c, consts.CodeConflict)
		}
		return true
	}

	// 用户未找到错误（业务层）
	var userNotFound *logic.UserNotFoundError
	if errors.As(err, &userNotFound) {
		response.Error(c, consts.CodeNotFound)
		return true
	}

	// 密码强度不足错误
	var weakPassword *logic.WeakPasswordError
	if errors.As(err, &weakPassword) {
		response.Error(c, consts.CodeParameterError)
		return true
	}

	// JWT令牌生成错误
	var tokenGenError *logic.TokenGenerationError
	if errors.As(err, &tokenGenError) {
		log.Printf("Token generation failed: %v", tokenGenError.Unwrap())
		response.Error(c, consts.CodeInternalError)
		return true
	}

	// 外部服务错误
	var externalServiceError *logic.ExternalServiceError
	if errors.As(err, &externalServiceError) {
		log.Printf("External service %s error: %v", externalServiceError.Service, externalServiceError.Unwrap())
		response.Error(c, consts.CodeInternalError)
		return true
	}

	return false
}

// handleRepositoryErrors 处理Repository层的自定义错误类型
func handleRepositoryErrors(c *gin.Context, err error) bool {
	// 用户未找到错误（数据层）
	var userNotFound *repositories.UserNotFoundError
	if errors.As(err, &userNotFound) {
		log.Printf("Repository user not found: %s", userNotFound.Error())
		response.Error(c, consts.CodeNotFound)
		return true
	}

	// 用户已存在错误（数据层）
	var userExists *repositories.UserExistsError
	if errors.As(err, &userExists) {
		log.Printf("Repository user exists: %s", userExists.Error())
		if userExists.Field == "username" {
			response.Error(c, consts.CodeUsernameExists)
		} else if userExists.Field == "email" {
			response.Error(c, consts.CodeEmailExists)
		} else {
			response.Error(c, consts.CodeConflict)
		}
		return true
	}

	// 数据库操作错误
	var dbError *repositories.DatabaseError
	if errors.As(err, &dbError) {
		log.Printf("Database error [%s on %s]: %v", dbError.Operation, dbError.Table, dbError.Unwrap())
		response.Error(c, consts.CodeInternalError)
		return true
	}

	return false
}

// handleCodeError 处理统一的 CodeError
func handleCodeError(c *gin.Context, codeErr *errs.CodeError) {
	// 记录完整的错误链路
	errorChain := buildErrorChain(codeErr)
	log.Printf("Error Chain [%s %s]: %s",
		c.Request.Method, c.Request.URL.Path, errorChain)

	// 获取映射配置
	mapping := errs.GetErrorMapping(codeErr.Code)

	// 返回脱敏的错误信息给前端
	response.ErrorWithCodeAndMessage(c, mapping.HTTPStatus, codeErr.Code, mapping.Message)
}

// buildErrorChain 构建完整的错误链路字符串
func buildErrorChain(err error) string {
	var chain []string
	current := err

	for current != nil {
		// 如果是 CodeError，记录其业务信息
		var codeErr *errs.CodeError
		if errors.As(current, &codeErr) {
			chain = append(chain, fmt.Sprintf("[%s: %s]", codeErr.Code, codeErr.Message))
			current = codeErr.Err
		} else {
			// 普通错误，直接记录
			chain = append(chain, fmt.Sprintf("[%v]", current))
			// 尝试展开
			current = errors.Unwrap(current)
		}
	}

	return strings.Join(chain, " -> ")
}
