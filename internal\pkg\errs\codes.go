package errs

// 文件模块错误码
const (
	CodeFileInvalidParams     = "FILE.INVALID_PARAMS"
	CodeFileUnsupportedType   = "FILE.UNSUPPORTED_TYPE"
	CodeFileNotFound          = "FILE.NOT_FOUND"
	CodeFileStorageError      = "FILE.STORAGE_ERROR"
	CodeFileGenerateURLError  = "FILE.GENERATE_URL_ERROR"
	CodeFileDeleteError       = "FILE.DELETE_ERROR"
)

// 用户模块错误码
const (
	CodeUserInvalidParams    = "USER.INVALID_PARAMS"
	CodeUserNotFound         = "USER.NOT_FOUND"
	CodeUserAlreadyExists    = "USER.ALREADY_EXISTS"
	CodeUserDisabled         = "USER.DISABLED"
	CodeUserEmailExists      = "USER.EMAIL_EXISTS"
	CodeUserWeakPassword     = "USER.WEAK_PASSWORD"
)

// 认证模块错误码
const (
	CodeAuthUserNotFound         = "AUTH.USER_NOT_FOUND"
	CodeAuthInvalidPassword      = "AUTH.INVALID_PASSWORD"
	CodeAuthTokenExpired         = "AUTH.TOKEN_EXPIRED"
	CodeAuthTokenInvalid         = "AUTH.TOKEN_INVALID"
	CodeAuthPermissionDenied     = "AUTH.PERMISSION_DENIED"
	CodeAuthTokenGenerationFailed = "AUTH.TOKEN_GENERATION_FAILED"
)

// 食物模块错误码
const (
	CodeFoodNotFound         = "FOOD.NOT_FOUND"
	CodeFoodInvalidParams    = "FOOD.INVALID_PARAMS"
	CodeFoodAlreadyExists    = "FOOD.ALREADY_EXISTS"
	CodeFoodCategoryNotFound = "FOOD.CATEGORY_NOT_FOUND"
)

// 饮食记录模块错误码
const (
	CodeDietRecordNotFound   = "DIET.RECORD_NOT_FOUND"
	CodeDietInvalidMealType  = "DIET.INVALID_MEAL_TYPE"
	CodeDietDuplicateRecord  = "DIET.DUPLICATE_RECORD"
	CodeDietInvalidDateRange = "DIET.INVALID_DATE_RANGE"
	CodeDietInvalidParams    = "DIET.INVALID_PARAMS"
)

// 营养模块错误码
const (
	CodeNutritionGoalNotFound     = "NUTRITION.GOAL_NOT_FOUND"
	CodeNutritionInvalidDateRange = "NUTRITION.INVALID_DATE_RANGE"
	CodeNutritionCalculationError = "NUTRITION.CALCULATION_ERROR"
	CodeNutritionInvalidParams    = "NUTRITION.INVALID_PARAMS"
)

// 系统错误码
const (
	CodeInternal             = "SYSTEM.INTERNAL_ERROR"
	CodeDatabaseError        = "SYSTEM.DATABASE_ERROR"
	CodeExternalServiceError = "SYSTEM.EXTERNAL_SERVICE_ERROR"
)
